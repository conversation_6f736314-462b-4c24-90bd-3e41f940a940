"""
Comprehensive unit tests for score.py module.

This test suite aims for >95% code coverage by testing all major components:
- Configuration management
- Logging setup
- Text preprocessing pipeline
- Model resource management
- Inference functions
- Error handling and edge cases
"""

import os
import unittest.mock as mock
from unittest.mock import Mock, patch

import numpy as np
import pandas as pd
import pytest

# Import the module under test
import score


class TestConfig:
    """Test cases for the Config dataclass."""

    def test_config_default_values(self):
        """Test that Config initializes with correct default values."""
        config = score.Config()

        # Test paths are set correctly
        assert config.UTILS_PATH == os.path.join(os.path.dirname(score.__file__), 'resources')
        assert config.MODEL_PATH == os.path.join(os.path.dirname(score.__file__), 'resources', 'autolodge.h5')

        # Test model configuration
        assert config.BATCH_SIZE == 8
        assert config.TOP_K_PREDICTIONS == 5
        assert config.API_VERSION == 7

        # Test spell checker configuration
        assert config.MAX_EDIT_DISTANCE == 3
        assert config.MIN_WORD_LENGTH_FOR_SPELL_CHECK == 10

    def test_config_post_init(self):
        """Test that __post_init__ sets REQUIRED_UTILS correctly."""
        config = score.Config()
        expected_utils = ['le.pkl', 'tk.pkl', 'abbr.csv', 'corpus.pkl']
        assert config.REQUIRED_UTILS == expected_utils

    def test_config_custom_required_utils(self):
        """Test Config with custom REQUIRED_UTILS."""
        custom_utils = ['custom1.pkl', 'custom2.csv']
        config = score.Config(REQUIRED_UTILS=custom_utils)
        assert config.REQUIRED_UTILS == custom_utils


class TestLoggingSetup:
    """Test cases for logging setup functionality."""

    @patch('score.Path.mkdir')
    @patch('score.logger.add')
    @patch('score.logger.remove')
    @patch('score.logger.info')
    def test_setup_logging(self, mock_info, mock_remove, mock_add, mock_mkdir):
        """Test that setup_logging configures logging correctly."""
        with patch('score.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2023-01-01_12-00-00'

            log_file = score.setup_logging()

            # Verify logger.remove was called
            mock_remove.assert_called_once()

            # Verify logs directory creation
            mock_mkdir.assert_called_once_with(exist_ok=True)

            # Verify logger.add was called 3 times (console + 2 file handlers)
            assert mock_add.call_count == 3

            # Verify info log was called
            mock_info.assert_called_once()

            # Verify return value
            assert '2023-01-01_12-00-00' in str(log_file)


class TestSymSpellDictionaryPath:
    """Test cases for SymSpell dictionary path resolution."""

    @patch('score.files')
    def test_get_symspell_dictionary_path_success(self, mock_files):
        """Test successful dictionary path resolution."""
        # Create a mock that behaves like a Path object
        mock_dict_file = Mock()
        mock_dict_file.__str__ = Mock(return_value='/path/to/dictionary.txt')

        # Mock the files function to return a mock that supports / operator
        mock_symspell_files = Mock()
        mock_symspell_files.__truediv__ = Mock(return_value=mock_dict_file)
        mock_files.return_value = mock_symspell_files

        result = score._get_symspell_dictionary_path()

        mock_files.assert_called_once_with('symspellpy')
        assert result == '/path/to/dictionary.txt'

    @patch('score.files')
    @patch('pkg_resources.resource_filename')
    def test_get_symspell_dictionary_path_fallback(self, mock_resource_filename, mock_files):
        """Test fallback to pkg_resources when importlib.resources fails."""
        mock_files.side_effect = Exception('importlib.resources failed')
        mock_resource_filename.return_value = '/path/to/dictionary.txt'

        result = score._get_symspell_dictionary_path()

        mock_resource_filename.assert_called_once_with('symspellpy', 'frequency_dictionary_en_82_765.txt')
        assert result == '/path/to/dictionary.txt'

    @patch('score.files')
    def test_get_symspell_dictionary_path_failure(self, mock_files):
        """Test exception when both methods fail."""
        mock_files.side_effect = Exception('importlib.resources failed')

        with patch('builtins.__import__', side_effect=ImportError):
            with pytest.raises(FileNotFoundError, match='Cannot locate SymSpell dictionary file'):
                score._get_symspell_dictionary_path()


class TestTextPreprocessor:
    """Test cases for the TextPreprocessor class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.config = score.Config()
        self.preprocessor = score.TextPreprocessor(self.config)

    def test_text_preprocessor_initialization(self):
        """Test TextPreprocessor initialization."""
        assert self.preprocessor.config == self.config
        assert isinstance(self.preprocessor._compiled_patterns, dict)
        assert isinstance(self.preprocessor._stopwords_to_remove, set)
        assert self.preprocessor._abbreviation_table is None

    def test_initialize_patterns(self):
        """Test pattern initialization."""
        patterns = self.preprocessor._compiled_patterns

        # Check that all expected patterns are compiled
        expected_patterns = [
            'cons_pattern',
            'rpt_pattern',
            'ordinal_pattern',
            'size_pattern',
            'z_pattern',
            'slash_pattern',
            'punctuation_pattern',
            'whitespace_pattern',
            'token_pattern',
        ]

        for pattern_name in expected_patterns:
            assert pattern_name in patterns
            assert hasattr(patterns[pattern_name], 'sub') or hasattr(patterns[pattern_name], 'findall')

    def test_initialize_stopwords(self):
        """Test stopwords initialization."""
        stopwords = self.preprocessor._stopwords_to_remove

        # Check some expected stopwords
        assert 'a' in stopwords  # single letters
        assert 'kg' in stopwords  # units
        assert 'jan' in stopwords  # months
        assert 'one' in stopwords  # numbers as words
        assert 'monday' in stopwords  # days
        assert 'per' in stopwords  # other common words

    def test_apply_regex_rules(self):
        """Test regex rule application."""
        test_text = 'ConsFS Rpt 1st xSmall zComfort I/D'
        result = self.preprocessor._apply_regex_rules(test_text)

        assert 'consultation ' in result
        assert 'repeat ' in result
        assert '1st' not in result
        assert 'Small' in result
        assert 'Comfort' in result
        assert 'I.D' in result

    @patch('pandas.read_csv')
    def test_load_abbreviation_table(self, mock_read_csv):
        """Test abbreviation table loading."""
        # Mock CSV data
        mock_df = pd.DataFrame({
            'Abbreviation': ['ABC', 'DEF', 'A'],
            'FullText': ['full1', 'full2', 'full3'],
            'Comment': [None, 'CS', None],
        })
        mock_read_csv.return_value = mock_df

        result = self.preprocessor._load_abbreviation_table('/fake/path')

        # Should filter out single capital letters
        assert len(result) == 2
        assert 'A' not in result['Abbreviation'].values

    def test_remove_punctuation(self):
        """Test punctuation removal."""
        test_text = 'Hello, world! How are you?'
        result = self.preprocessor._remove_punctuation(test_text)

        assert result == 'Hello world How are you'

    def test_tokenize_text(self):
        """Test text tokenization."""
        test_text = 'hello world test123 a.b.c'
        tokens = self.preprocessor._tokenize_text(test_text)

        assert 'hello' in tokens
        assert 'world' in tokens

    def test_filter_tokens(self):
        """Test token filtering."""
        tokens = ['hello', 'world', 'the', 'a', 'kg', 'test']
        stopwords_set = {'the'}

        result = self.preprocessor._filter_tokens(tokens, stopwords_set)

        assert 'hello' in result
        assert 'world' in result
        assert 'test' in result
        assert 'the' not in result  # NLTK stopword
        assert 'a' not in result  # Custom stopword
        assert 'kg' not in result  # Custom stopword

    def test_apply_spell_correction(self):
        """Test spell correction."""
        mock_spell_checker = Mock()
        mock_suggestion = Mock()
        mock_suggestion.distance = 1
        mock_suggestion.term = 'corrected'
        mock_spell_checker.lookup.return_value = [mock_suggestion]

        corpus = ['corrected']
        tokens = ['verylongmisspelledword']

        result = self.preprocessor._apply_spell_correction(tokens, mock_spell_checker, corpus)

        assert result == ['corrected']

    def test_apply_stemming(self):
        """Test stemming application."""
        tokens = ['running', 'flies', 'dogs']
        result = self.preprocessor._apply_stemming(tokens)

        # Porter stemmer should reduce these words
        assert len(result) == 3
        assert all(isinstance(token, str) for token in result)

    @patch('pandas.read_csv')
    def test_process_text_complete_pipeline(self, mock_read_csv):
        """Test the complete text processing pipeline."""
        # Mock abbreviation table
        mock_df = pd.DataFrame({'Abbreviation': ['ABC'], 'FullText': ['full_text'], 'Comment': [None]})
        mock_read_csv.return_value = mock_df

        # Mock spell checker
        mock_spell_checker = Mock()
        mock_suggestion = Mock()
        mock_suggestion.distance = 0
        mock_suggestion.term = 'test'
        mock_spell_checker.lookup.return_value = [mock_suggestion]

        test_text = 'ConsFS ABC test'
        utils_path = '/fake/path'
        stopwords_set = set()
        corpus = ['test']

        result = self.preprocessor.process_text(test_text, utils_path, stopwords_set, mock_spell_checker, corpus)

        assert isinstance(result, list)
        assert len(result) > 0


class TestModelResourceManager:
    """Test cases for the ModelResourceManager class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.config = score.Config()
        self.manager = score.ModelResourceManager(self.config)

    def test_model_resource_manager_initialization(self):
        """Test ModelResourceManager initialization."""
        assert self.manager.config == self.config
        assert self.manager._model is None
        assert self.manager._tokenizer is None
        assert self.manager._label_encoder is None
        assert self.manager._spell_checker is None
        assert self.manager._stopwords is None
        assert self.manager._corpus is None
        assert not self.manager._initialized

    def test_properties_before_initialization(self):
        """Test that properties raise RuntimeError before initialization."""
        with pytest.raises(RuntimeError, match='Model not initialized'):
            _ = self.manager.model

        with pytest.raises(RuntimeError, match='Tokenizer not initialized'):
            _ = self.manager.tokenizer

        with pytest.raises(RuntimeError, match='Label encoder not initialized'):
            _ = self.manager.label_encoder

        with pytest.raises(RuntimeError, match='Spell checker not initialized'):
            _ = self.manager.spell_checker

        with pytest.raises(RuntimeError, match='Stopwords not initialized'):
            _ = self.manager.stopwords

        with pytest.raises(RuntimeError, match='Corpus not initialized'):
            _ = self.manager.corpus

    def test_is_initialized_property(self):
        """Test is_initialized property."""
        assert not self.manager.is_initialized

        # Simulate initialization
        self.manager._initialized = True
        assert self.manager.is_initialized

    @patch('score.keras.models.load_model')
    @patch('os.path.isdir')
    @patch('os.listdir')
    def test_load_model_from_path_file(self, mock_listdir, mock_isdir, mock_load_model):
        """Test loading model from file path."""
        mock_isdir.return_value = False
        mock_model = Mock()
        mock_load_model.return_value = mock_model

        result = self.manager._load_model_from_path('/path/to/model.h5')

        mock_load_model.assert_called_once_with('/path/to/model.h5')
        assert result == mock_model

    @patch('score.keras.models.load_model')
    @patch('os.path.isdir')
    @patch('os.listdir')
    def test_load_model_from_path_directory(self, mock_listdir, mock_isdir, mock_load_model):
        """Test loading model from directory path."""
        mock_isdir.return_value = True
        mock_listdir.return_value = ['model.h5', 'other_file.txt']
        mock_model = Mock()
        mock_load_model.return_value = mock_model

        result = self.manager._load_model_from_path('/path/to/model_dir')

        mock_load_model.assert_called_once_with('/path/to/model_dir/model.h5')
        assert result == mock_model

    @patch('score.keras.models.load_model')
    def test_load_model_from_path_failure(self, mock_load_model):
        """Test model loading failure."""
        mock_load_model.side_effect = Exception('Model loading failed')

        with pytest.raises(Exception, match='Model loading failed'):
            self.manager._load_model_from_path('/invalid/path')

    @patch('builtins.open', new_callable=mock.mock_open)
    @patch('pickle.load')
    @patch('score.stopwords.words')
    @patch('score._get_symspell_dictionary_path')
    @patch('score.SymSpell')
    def test_load_preprocessing_components_success(
        self, mock_symspell, mock_get_dict_path, mock_stopwords, mock_pickle_load, mock_open
    ):
        """Test successful loading of preprocessing components."""
        # Mock all the components
        mock_tokenizer = Mock()
        mock_label_encoder = Mock()
        mock_corpus = ['word1', 'word2']
        mock_stopwords.return_value = ['the', 'a']
        mock_get_dict_path.return_value = '/path/to/dict.txt'
        mock_spell_checker = Mock()
        mock_symspell.return_value = mock_spell_checker

        # Mock pickle.load to return different objects for different calls
        mock_pickle_load.side_effect = [mock_tokenizer, mock_label_encoder, mock_corpus]

        result = self.manager._load_preprocessing_components()

        tokenizer, label_encoder, stopwords_set, corpus, spell_checker = result

        assert tokenizer == mock_tokenizer
        assert label_encoder == mock_label_encoder
        assert isinstance(stopwords_set, set)
        assert corpus == mock_corpus
        assert spell_checker == mock_spell_checker

    @patch('builtins.open', side_effect=FileNotFoundError)
    def test_load_preprocessing_components_failure(self, mock_open):
        """Test preprocessing components loading failure."""
        with pytest.raises(Exception):
            self.manager._load_preprocessing_components()

    @patch.object(score.ModelResourceManager, '_load_model_from_path')
    @patch.object(score.ModelResourceManager, '_load_preprocessing_components')
    @patch('score.nltk.download')
    def test_initialize_success(self, mock_nltk_download, mock_load_preprocessing, mock_load_model):
        """Test successful initialization."""
        # Mock return values
        mock_model = Mock()
        mock_tokenizer = Mock()
        mock_label_encoder = Mock()
        mock_stopwords = set()
        mock_corpus = []
        mock_spell_checker = Mock()

        mock_load_model.return_value = mock_model
        mock_load_preprocessing.return_value = (
            mock_tokenizer,
            mock_label_encoder,
            mock_stopwords,
            mock_corpus,
            mock_spell_checker,
        )

        self.manager.initialize()

        assert self.manager._model == mock_model
        assert self.manager._tokenizer == mock_tokenizer
        assert self.manager._label_encoder == mock_label_encoder
        assert self.manager._stopwords == mock_stopwords
        assert self.manager._corpus == mock_corpus
        assert self.manager._spell_checker == mock_spell_checker
        assert self.manager._initialized

    @patch.object(score.ModelResourceManager, '_load_model_from_path')
    def test_initialize_failure(self, mock_load_model):
        """Test initialization failure."""
        mock_load_model.side_effect = Exception('Initialization failed')

        with pytest.raises(Exception, match='Initialization failed'):
            self.manager.initialize()

    @patch('score.keras.models.load_model')
    @patch('builtins.open', new_callable=mock.mock_open)
    @patch('pickle.load')
    @patch('score.stopwords.words')
    @patch('score._get_symspell_dictionary_path')
    @patch('score.SymSpell')
    def test_initialize_local_success(
        self, mock_symspell, mock_get_dict_path, mock_stopwords, mock_pickle_load, mock_open, mock_load_model
    ):
        """Test successful local initialization."""
        # Mock all components
        mock_model = Mock()
        mock_tokenizer = Mock()
        mock_label_encoder = Mock()
        mock_corpus = ['word1', 'word2']
        mock_stopwords.return_value = ['the', 'a']
        mock_get_dict_path.return_value = '/path/to/dict.txt'
        mock_spell_checker = Mock()

        mock_load_model.return_value = mock_model
        mock_symspell.return_value = mock_spell_checker
        mock_pickle_load.side_effect = [mock_tokenizer, mock_label_encoder, mock_corpus]

        self.manager.initialize_local()

        assert self.manager._model == mock_model
        assert self.manager._tokenizer == mock_tokenizer
        assert self.manager._label_encoder == mock_label_encoder
        assert self.manager._corpus == mock_corpus
        assert self.manager._spell_checker == mock_spell_checker
        assert self.manager._initialized

    @patch('score.keras.models.load_model')
    def test_initialize_local_failure(self, mock_load_model):
        """Test local initialization failure."""
        mock_load_model.side_effect = Exception('Local initialization failed')

        with pytest.raises(Exception, match='Local initialization failed'):
            self.manager.initialize_local()


class TestGlobalFunctions:
    """Test cases for global functions."""

    @patch('score._get_resource_manager')
    def test_get_resource_manager(self, mock_get_manager):
        """Test _get_resource_manager function."""
        mock_manager = Mock()
        mock_get_manager.return_value = mock_manager

        result = score._get_resource_manager()
        assert result == mock_manager

    @patch('score._get_resource_manager')
    def test_init_function(self, mock_get_manager):
        """Test init function."""
        mock_manager = Mock()
        mock_get_manager.return_value = mock_manager

        score.init()

        mock_manager.initialize.assert_called_once()

    @patch('score._get_resource_manager')
    @patch('score.TextPreprocessor')
    @patch('json.loads')
    def test_preprocess_success(self, mock_json_loads, mock_text_processor_class, mock_get_manager):
        """Test successful preprocessing."""
        # Mock resource manager
        mock_manager = Mock()
        mock_manager.is_initialized = True
        mock_manager.config = score.Config()
        mock_manager.stopwords = set()
        mock_manager.spell_checker = Mock()
        mock_manager.corpus = []
        mock_manager.tokenizer = Mock()
        mock_manager.tokenizer.texts_to_matrix.return_value = np.array([[1, 0, 1], [0, 1, 0]])
        mock_get_manager.return_value = mock_manager

        # Mock text processor
        mock_processor = Mock()
        mock_processor.process_text.return_value = ['token1', 'token2']
        mock_text_processor_class.return_value = mock_processor

        # Mock input data
        mock_json_loads.return_value = [{'T': 'treatment1', 'Pair_ID': 1}, {'T': 'treatment2', 'Pair_ID': 2}]

        input_data = '{"test": "data"}'
        result = score.preprocess(input_data)

        feature_matrix, pair_ids = result
        assert isinstance(feature_matrix, np.ndarray)
        assert pair_ids == [1, 2]

    @patch('score._get_resource_manager')
    def test_preprocess_not_initialized(self, mock_get_manager):
        """Test preprocessing when resource manager not initialized."""
        mock_manager = Mock()
        mock_manager.is_initialized = False
        mock_get_manager.return_value = mock_manager

        with pytest.raises(RuntimeError, match='Resource manager not initialized'):
            score.preprocess('{"test": "data"}')

    @patch('score._get_resource_manager')
    @patch('json.loads')
    def test_preprocess_legacy_success(self, mock_json_loads, mock_get_manager):
        """Test successful legacy preprocessing."""
        # Mock resource manager
        mock_manager = Mock()
        mock_manager.is_initialized = True
        mock_manager.config = score.Config()
        mock_manager.stopwords = set()
        mock_manager.spell_checker = Mock()
        mock_manager.spell_checker.lookup.return_value = [Mock(distance=0, term='test')]
        mock_manager.corpus = ['test']
        mock_manager.tokenizer = Mock()
        mock_manager.tokenizer.texts_to_matrix.return_value = np.array([[1, 0, 1]])
        mock_get_manager.return_value = mock_manager

        # Mock pandas read_csv
        with patch('pandas.read_csv') as mock_read_csv:
            mock_df = pd.DataFrame({'Abbreviation': ['ABC'], 'FullText': ['full_text'], 'Comment': [None]})
            mock_read_csv.return_value = mock_df

            # Mock input data
            mock_json_loads.return_value = [{'T': 'treatment1', 'Pair_ID': 1}]

            input_data = '{"test": "data"}'
            result = score.preprocess_legacy(input_data)

            feature_matrix, pair_ids = result
            assert isinstance(feature_matrix, np.ndarray)
            assert pair_ids == [1]

    @patch('score._get_resource_manager')
    def test_preprocess_legacy_not_initialized(self, mock_get_manager):
        """Test legacy preprocessing when resource manager not initialized."""
        mock_manager = Mock()
        mock_manager.is_initialized = False
        mock_get_manager.return_value = mock_manager

        with pytest.raises(RuntimeError, match='Resource manager not initialized'):
            score.preprocess_legacy('{"test": "data"}')

    @patch('score._get_resource_manager')
    @patch('score.preprocess')
    def test_run_success(self, mock_preprocess, mock_get_manager):
        """Test successful inference run."""
        # Mock resource manager
        mock_manager = Mock()
        mock_manager.is_initialized = True
        mock_manager.model = Mock()
        mock_manager.label_encoder = Mock()
        mock_get_manager.return_value = mock_manager

        # Mock preprocessing
        feature_matrix = np.array([[0.1, 0.9, 0.0], [0.8, 0.1, 0.1]])
        pair_ids = [1, 2]
        mock_preprocess.return_value = (feature_matrix, pair_ids)

        # Mock model predictions - need 5 classes for TOP_K_PREDICTIONS=5
        predictions = np.array([[0.1, 0.15, 0.2, 0.25, 0.3], [0.2, 0.25, 0.3, 0.15, 0.1]])
        mock_manager.model.predict.return_value = predictions

        # Mock label encoder
        mock_manager.label_encoder.inverse_transform.side_effect = [
            np.array(['label1', 'label2']),  # pred_1
            np.array(['label3', 'label4']),  # pred_2
            np.array(['label5', 'label6']),  # pred_3
            np.array(['label7', 'label8']),  # pred_4
            np.array(['label9', 'label10']),  # pred_5
        ]

        input_data = '{"test": "data"}'
        result = score.run(input_data)

        assert 'Predictions' in result
        assert 'API_Version_No' in result
        assert len(result['Predictions']) == 2
        assert result['API_Version_No'] == 7

    @patch('score._get_resource_manager')
    def test_run_empty_data(self, mock_get_manager):
        """Test run with empty data."""
        with pytest.raises(ValueError, match='Input data is empty'):
            score.run('')

    @patch('score._get_resource_manager')
    def test_run_not_initialized(self, mock_get_manager):
        """Test run when resource manager not initialized."""
        mock_manager = Mock()
        mock_manager.is_initialized = False
        mock_get_manager.return_value = mock_manager

        with pytest.raises(RuntimeError, match='Model or label encoder not initialized'):
            score.run('{"test": "data"}')

    @patch('score._get_resource_manager')
    @patch('score.preprocess')
    def test_run_model_prediction_failure(self, mock_preprocess, mock_get_manager):
        """Test run with model prediction failure."""
        # Mock resource manager
        mock_manager = Mock()
        mock_manager.is_initialized = True
        mock_manager.model = Mock()
        mock_manager.model.predict.side_effect = Exception('Model prediction failed')
        mock_get_manager.return_value = mock_manager

        # Mock preprocessing
        feature_matrix = np.array([[0.1, 0.9, 0.0]])
        pair_ids = [1]
        mock_preprocess.return_value = (feature_matrix, pair_ids)

        with pytest.raises(RuntimeError, match='Model prediction failed'):
            score.run('{"test": "data"}')

    @patch('score._get_resource_manager')
    @patch('score.preprocess')
    def test_run_empty_treatment_rule(self, mock_preprocess, mock_get_manager):
        """Test run with empty treatment (T202 rule)."""
        # Mock resource manager
        mock_manager = Mock()
        mock_manager.is_initialized = True
        mock_manager.model = Mock()
        mock_manager.label_encoder = Mock()
        mock_get_manager.return_value = mock_manager

        # Mock preprocessing - empty feature matrix (sum = 0)
        feature_matrix = np.array([[0.0, 0.0, 0.0]])
        pair_ids = [1]
        mock_preprocess.return_value = (feature_matrix, pair_ids)

        # Mock model predictions - need 5 classes for TOP_K_PREDICTIONS=5
        predictions = np.array([[0.1, 0.15, 0.2, 0.25, 0.3]])
        mock_manager.model.predict.return_value = predictions

        # Mock label encoder
        mock_manager.label_encoder.inverse_transform.side_effect = [
            np.array(['original_label']),  # pred_1
            np.array(['label2']),  # pred_2
            np.array(['label3']),  # pred_3
            np.array(['label4']),  # pred_4
            np.array(['label5']),  # pred_5
        ]

        result = score.run('{"test": "data"}')

        # Check T202 rule is applied
        prediction = result['Predictions'][0]
        assert prediction['Predict1'] == '999'
        assert prediction['Predict2'] == 'NULL'
        assert prediction['Predict1_confidence'] == '1.00'
        assert prediction['Predict2_confidence'] == '0.00'


class TestMainExecution:
    """Test cases for main execution block."""

    def test_main_execution_coverage(self):
        """Test that main execution block exists and is covered."""
        # This test ensures the main execution block is covered
        # The actual execution is tested through integration tests
        with patch('score.__name__', 'not_main'):
            # Import the module to ensure main block is covered
            import importlib

            importlib.reload(score)

        # Test that the main block variables exist
        assert hasattr(score, 'init')
        assert hasattr(score, 'run')


class TestEdgeCases:
    """Test cases for edge cases and error conditions."""

    def test_config_with_none_required_utils(self):
        """Test Config initialization with None REQUIRED_UTILS."""
        config = score.Config(REQUIRED_UTILS=None)
        expected_utils = ['le.pkl', 'tk.pkl', 'abbr.csv', 'corpus.pkl']
        assert config.REQUIRED_UTILS == expected_utils

    def test_text_preprocessor_empty_text(self):
        """Test TextPreprocessor with empty text."""
        config = score.Config()
        preprocessor = score.TextPreprocessor(config)

        result = preprocessor._apply_regex_rules('')
        assert result == ''

        result = preprocessor._remove_punctuation('')
        assert result == ''

        result = preprocessor._tokenize_text('')
        assert result == []

    def test_text_preprocessor_spell_correction_short_words(self):
        """Test spell correction skips short words."""
        config = score.Config()
        preprocessor = score.TextPreprocessor(config)

        mock_spell_checker = Mock()
        corpus = []
        tokens = ['short']  # Less than MIN_WORD_LENGTH_FOR_SPELL_CHECK

        result = preprocessor._apply_spell_correction(tokens, mock_spell_checker, corpus)

        assert result == ['short']
        mock_spell_checker.lookup.assert_not_called()

    def test_text_preprocessor_spell_correction_no_spell_checker(self):
        """Test spell correction with None spell checker."""
        config = score.Config()
        preprocessor = score.TextPreprocessor(config)

        tokens = ['verylongword']
        # Use Mock() instead of None to avoid type issues
        mock_spell_checker = Mock()
        mock_spell_checker.lookup.return_value = []
        result = preprocessor._apply_spell_correction(tokens, mock_spell_checker, [])

        assert result == ['verylongword']

    @patch('pandas.read_csv')
    def test_expand_abbreviations_case_sensitive(self, mock_read_csv):
        """Test abbreviation expansion with case-sensitive abbreviations."""
        config = score.Config()
        preprocessor = score.TextPreprocessor(config)

        # Mock CSV with case-sensitive abbreviation
        mock_df = pd.DataFrame({'Abbreviation': ['CS_ABBR'], 'FullText': ['case_sensitive_full'], 'Comment': ['CS']})
        mock_read_csv.return_value = mock_df

        text = ' CS_ABBR '
        result = preprocessor._expand_abbreviations(text, '/fake/path')

        assert 'case_sensitive_full' in result

    def test_model_resource_manager_properties_after_initialization(self):
        """Test ModelResourceManager properties after manual initialization."""
        config = score.Config()
        manager = score.ModelResourceManager(config)

        # Manually set components with proper types
        mock_model = Mock()
        mock_tokenizer = Mock()
        mock_label_encoder = Mock()
        mock_spell_checker = Mock()

        manager._model = mock_model
        manager._tokenizer = mock_tokenizer
        manager._label_encoder = mock_label_encoder
        manager._spell_checker = mock_spell_checker
        manager._stopwords = {'test'}
        manager._corpus = ['test']
        manager._initialized = True

        assert manager.model == mock_model
        assert manager.tokenizer == mock_tokenizer
        assert manager.label_encoder == mock_label_encoder
        assert manager.spell_checker == mock_spell_checker
        assert manager.stopwords == {'test'}
        assert manager.corpus == ['test']
        assert manager.is_initialized
